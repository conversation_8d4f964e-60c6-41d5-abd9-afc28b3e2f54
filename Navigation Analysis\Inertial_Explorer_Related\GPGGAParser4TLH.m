classdef GPGGAParser4TLH
    % GPGGAParser 类用于解析TLH定制的 GPGGA 数据帧
    properties
        frameId          % 帧计数
        gnssSecond       % GNSS周内秒
        latitude         % 纬度 (十进制)，存储为数组
        longitude        % 经度 (十进制)，存储为数组
        altitude         % 海拔 (十进制)，
        height           % 高度 (米)，存储为数组
        numSatellites    % 卫星数，存储为数组
        hdop             % 双差分精度因子（Double Difference DOP），存储为数组
        velocity         % 导航速度 (米/秒)，存储为数组，目前顺序是东北天
        roll             % 横滚角 (度)，存储为数组
        pitch            % 俯仰角 (度)，存储为数组
        heading          % 航向角 (度)，存储为数组
    end

    methods
        function obj = GPGGAParser4TLH(filePath)
            % GPGGAParser4TLH 构造函数
            % 输入:
            %   filePath - .gga 文件的路径
            % 功能:
            %   1. 读取文件内容
            %   2. 提取所有 GPGGA 数据帧
            %   3. 解析每个 GPGGA 数据帧并存储为矢量形式

            % 检查文件是否存在
            if exist(filePath, 'file') ~= 2
                error('文件不存在: %s', filePath);
            end

            % 打开文件并读取内容
            fileID = fopen(filePath, 'r');
            fileContent = fread(fileID, '*char')'; % 读取文件内容为字符串
            fclose(fileID);

            % 查找所有 GPGGA 数据帧
            gpggaIndices = strfind(fileContent, '$GPGGA');
            if isempty(gpggaIndices)
                error('文件中未找到 GPGGA 数据帧');
            end

            % 初始化存储变量
            numFrames = length(gpggaIndices);
            obj.gnssSecond = zeros(numFrames, 1);
            obj.latitude = zeros(numFrames, 1);
            obj.longitude = zeros(numFrames, 1);
            obj.height = zeros(numFrames, 1);
            obj.altitude = zeros(numFrames, 1);
            obj.numSatellites = zeros(numFrames, 1);
            obj.hdop = zeros(numFrames, 1);
            obj.velocity = zeros(numFrames, 3);
            obj.roll = zeros(numFrames, 1);
            obj.pitch = zeros(numFrames, 1);
            obj.heading = zeros(numFrames, 1);

            % 解析每个 GPGGA 数据帧
            for i = 1:numFrames
                % 提取当前 GPGGA 数据帧
                startIdx = gpggaIndices(i);
                if i < numFrames
                    endIdx = gpggaIndices(i+1) - 1;
                else
                    endIdx = length(fileContent);
                end
                gpggaString = fileContent(startIdx:endIdx);
                gpggaString = strtok(gpggaString, [char(13), newline]); % 去掉换行符

                % 解析 GPGGA 数据帧
                obj = obj.parseGPGGA(gpggaString, i);
            end
        end

        function obj = parseGPGGA(obj, gpggaString, frameIndex)
            % parseGPGGA 解析 GPGGA 数据帧
            % 输入:
            %   gpggaString - GPGGA 数据帧字符串
            %   frameIndex - 当前数据帧的索引
            % 输出:
            %   obj - 解析后的 GPGGA 数据对象

            % 按逗号分割字符串
            tokens = strsplit(gpggaString, ' ');

            % 解析帧计数
            frame_id = str2double(tokens{15});
            obj.frameId(frameIndex) = frame_id;

            % 解析GNSS周内秒
            gnssSec = str2double(tokens{2});
            obj.gnssSecond(frameIndex) = gnssSec; % UTC 时间 (hhmmss.ss)

            % 解析纬度
            latDeg = str2double(tokens{6}); % 纬度度数
            obj.latitude(frameIndex) = latDeg;

            % 解析经度
            lonDeg = str2double(tokens{5}); % 经度度数
            obj.longitude(frameIndex) = lonDeg;

            % 解析正高(Orthometric Height 即海拔高)
            obj.altitude(frameIndex) = str2double(tokens{7}); % 海拔高 (米)

            % 解析椭球高(Ellipsoidal Height)
            obj.height(frameIndex) = str2double(tokens{8}); % 椭球高 (米)

            % 解析卫星数
            obj.numSatellites(frameIndex) = str2double(tokens{3}); % 卫星数

            % 解析双差分精度因子（Double Difference DOP）
            obj.hdop(frameIndex) = str2double(tokens{4}); % 双差分精度因子（Double Difference DOP）

            % 解析东向速度
            velocityEast = str2double(tokens{9}); % 东向速度 (米/秒)

            % 解析北向速度
            velocityNorth = str2double(tokens{10}); % 北向速度 (米/秒)

            % 解析天向速度
            velocityUp = str2double(tokens{11}); % 天向速度 (米/秒)

            obj.velocity(frameIndex,:) = horzcat(velocityEast,velocityNorth,velocityUp); 

            % 解析横滚角
            obj.roll(frameIndex) = str2double(tokens{13}); % 横滚角 (度)

            % 解析俯仰角
            obj.pitch(frameIndex) = str2double(tokens{12}); % 俯仰角 (度)

            % 解析航向角
            obj.heading(frameIndex) = str2double(tokens{14}); % 航向角 (度)

            % 在解析完所有帧后可调用时间戳检验
            if frameIndex == length(obj.gnssSecond)
                [isValid, ~] = obj.checkGnssTimeInterval();
                if ~isValid
                    warning('GPGGA数据存在时间戳异常或丢帧。');
                end
            end
        end

        % function compareWithRTK(obj, rtkNavFilePath)
        % 
        %     rtkNav.data = readmatrix(rtkNavFilePath);
        %     rtkNav.gnssSecond = rtkNav.data(:,20);
        %     rtkNav.longitude= rtkNav.data(:,10);
        %     rtkNav.latitude = rtkNav.data(:,11);
        %     rtkNav.height = rtkNav.data(:,12);
        %     rtkNav.roll     = rtkNav.data(:,1);
        %     rtkNav.pitch    = rtkNav.data(:,2);
        %     rtkNav.yaw  = rtkNav.data(:,3);
        %     % if rtkNav.yaw >= 180
        %     %     rtkNav.heading = 360 - rtkNav.yaw;
        %     % else
        %     %     rtkNav.heading = -1.0 * rtkNav.yaw;
        %     % end
        %     rtkNav.heading = rtkNav.yaw;
        %     rtkNav.velocity = rtkNav.data(:,13:15);
        % 
        %     idx = find(rtkNav.gnssSecond == obj.gnssSecond(1), 1);
        %     [x, y, z] = geodetic2ecef(wgs84Ellipsoid, rtkNav.latitude, rtkNav.longitude, rtkNav.height);
        %     rtkNav.pos = [x, y, z];
        %     [x, y, z] = geodetic2ecef(wgs84Ellipsoid, obj.latitude, obj.longitude, obj.height);
        %     pos = [x, y, z];
        %     posDiff = vecnorm(rtkNav.pos(idx:200:end,:)-pos, 2, 2);
        %     figure;hold on
        %     posDiffLine = plot(obj.gnssSecond, posDiff);
        %     set(posDiffLine,"DisplayName","PPK与RTK的位置差值")
        %     ylabel("位置差值(m)")
        %     legend;grid on
        % 
        %     % for axis_idx = 1:3
        %     %     figure;hold on
        %     %     tcLonLine   = plot(obj.gnssSecond, obj.velocity(:,axis_idx));
        %     %     set(tcLonLine,"DisplayName","PPK速度曲线")
        %     %     rtkLonLine  = plot(rtkNav.gnssSecond, rtkNav.velocity(:,axis_idx));
        %     %     set(rtkLonLine,"DisplayName","RTK速度曲线")
        %     %     yyaxis right
        %     %     velDiff = rtkNav.velocity(idx:200:end,axis_idx) - obj.velocity(:,axis_idx);
        %     %     velDiffLine = plot(obj.gnssSecond, velDiff);
        %     %     set(velDiffLine,"DisplayName","PPK-RTK速度差")
        %     %     legend
        %     % end
        % 
        %     % 定义要对比的变量列表
        %     variables = {'longitude', 'latitude', 'height', 'roll', 'pitch', 'heading'};
        %     titles = {'经度', '纬度', '高度', '横滚角', '俯仰角', '航向角'};
        % 
        %     % 遍历每个变量
        %     for i = 1:length(variables)
        %         figure; hold on;
        % 
        %         % 获取当前变量名
        %         varName = variables{i};
        % 
        %         % 绘制 PPK 紧耦合数据
        %         ppkLine = plot(obj.gnssSecond, obj.(varName));
        %         set(ppkLine, 'DisplayName', ['PPK紧耦合', titles{i}]);
        % 
        %         % 绘制 RTK 数据
        %         rtkLine = plot(rtkNav.gnssSecond, rtkNav.(varName));
        %         set(rtkLine, 'DisplayName', ['RTK ', titles{i}]);
        % 
        %         % 切换到右侧 Y 轴绘制差值
        %         yyaxis right;
        %         diffData = rtkNav.(varName)(idx:200:end) - obj.(varName);
        %         diffLine = plot(obj.gnssSecond, diffData);
        %         set(diffLine, 'DisplayName', [titles{i}, '差值']);
        % 
        %         % 添加图例和标题
        %         legend;
        %         title([titles{i}, '对比']);
        %         xlabel('时间 (秒)');
        %         yyaxis left; ylabel(titles{i});
        %         yyaxis right; ylabel([titles{i}, '差值']);
        % 
        %         hold off;
        %     end
        % end
    
        % function compareWithRTKfromMD01(obj, rtkNavFilePath)
        % 
        %     rtkNav.data = readmatrix(rtkNavFilePath);
        %     rtkNav.gnssSecond = rtkNav.data(:,18);
        %     rtkNav.longitude= rtkNav.data(:,11);
        %     rtkNav.latitude = rtkNav.data(:,12);
        %     rtkNav.altitude = rtkNav.data(:,13);
        %     rtkNav.roll     = rtkNav.data(:,2);
        %     rtkNav.pitch    = rtkNav.data(:,3);
        %     rtkNav.yaw      = rtkNav.data(:,4);
        %     if rtkNav.yaw >= 180
        %         rtkNav.heading = 360 - rtkNav.yaw;
        %     else
        %         rtkNav.heading = -1.0 * rtkNav.yaw;
        %     end
        %     rtkNav.velocity = rtkNav.data(:,14:16);
        % 
        %     idx = find(rtkNav.gnssSecond == obj.gnssSecond(1), 1);
        %     idx_= find(rtkNav.gnssSecond == obj.gnssSecond(end), 1);
        %     [x, y, z] = geodetic2ecef(wgs84Ellipsoid, rtkNav.latitude, rtkNav.longitude, rtkNav.altitude);
        %     rtkNav.pos = [x, y, z];
        %     [x, y, z] = geodetic2ecef(wgs84Ellipsoid, obj.latitude, obj.longitude, obj.altitude);
        %     pos = [x, y, z];   
        %     posDiff = vecnorm(rtkNav.pos(idx:idx_+1,:)-pos, 2, 2);
        %     figure;hold on
        %     posDiffLine = plot(obj.gnssSecond, posDiff);
        %     set(posDiffLine,"DisplayName","PPK与RTK的位置差值")
        %     ylabel("位置差值(m)")
        %     legend;grid on
        % 
        %     % for axis_idx = 1:3
        %     %     figure;hold on
        %     %     tcLonLine   = plot(obj.gnssSecond, obj.velocity(:,axis_idx));
        %     %     set(tcLonLine,"DisplayName","PPK速度曲线")
        %     %     rtkLonLine  = plot(rtkNav.gnssSecond, rtkNav.velocity(:,axis_idx));
        %     %     set(rtkLonLine,"DisplayName","RTK速度曲线")
        %     %     yyaxis right
        %     %     velDiff = rtkNav.velocity(idx:200:end,axis_idx) - obj.velocity(:,axis_idx);
        %     %     velDiffLine = plot(obj.gnssSecond, velDiff);
        %     %     set(velDiffLine,"DisplayName","PPK-RTK速度差")
        %     %     legend
        %     % end
        % 
        %     % 定义要对比的变量列表
        %     variables = {'longitude', 'latitude', 'altitude', 'roll', 'pitch', 'heading'};
        %     titles = {'经度', '纬度', '海拔高', '横滚角', '俯仰角', '航向角'};
        % 
        %     % 遍历每个变量
        %     for i = 1:length(variables)
        %         figure; hold on;
        % 
        %         % 获取当前变量名
        %         varName = variables{i};
        % 
        %         % 绘制 PPK 紧耦合数据
        %         ppkLine = plot(obj.gnssSecond, obj.(varName));
        %         set(ppkLine, 'DisplayName', ['PPK紧耦合', titles{i}]);
        % 
        %         % 绘制 RTK 数据
        %         rtkLine = plot(rtkNav.gnssSecond, rtkNav.(varName));
        %         set(rtkLine, 'DisplayName', ['RTK ', titles{i}]);
        % 
        %         % 切换到右侧 Y 轴绘制差值
        %         yyaxis right;
        %         diffData = rtkNav.(varName)(idx:idx_+1) - obj.(varName);
        %         diffLine = plot(obj.gnssSecond, diffData);
        %         set(diffLine, 'DisplayName', [titles{i}, '差值']);
        % 
        %         % 添加图例和标题
        %         legend;
        %         title([titles{i}, '对比']);
        %         xlabel('时间 (秒)');
        %         yyaxis left; ylabel(titles{i});
        %         yyaxis right; ylabel([titles{i}, '差值']);
        % 
        %         hold off;
        %     end
        % end
        
        function navResult = toComNavResult(obj)
            % toComNavResult - 输出为ComNavResult类对象
            navResult = ComNavResult();
            % GNSS周内秒
            navResult.gnssTow = obj.gnssSecond;
            % 卫星数
            navResult.satNum = obj.numSatellites;
            % 位置 Nx3: [经度, 纬度, 高度]
            navResult.position = [obj.longitude, obj.latitude, obj.height];
            % 速度 Nx3: [东, 北, 天]
            navResult.velocity = obj.velocity;
            % 姿态 Nx3: [横滚, 俯仰, 航向]
            navResult.attitude = [obj.roll, obj.pitch, obj.heading];
            % GNSSdata结构体
            navResult.GNSSdata = struct('Position', [], ...
                                        'Velocity', [], ...
                                        'Attitude', []);
            % 输出频率估算（如有gnssSecond可用）
            if length(obj.gnssSecond) > 1
                dt = diff(obj.gnssSecond);
                navResult.outputRate = 1/mean(dt(dt>0));
            else
                navResult.outputRate = [];
            end
        end
        
        function [isValid, details] = checkGnssTimeInterval(obj)
            % 检查gnssSecond数据的时间间隔连续性，检测丢帧
            gnssTimeData = obj.gnssSecond;
            timeIntervals = diff(gnssTimeData);
            [counts, values] = hist(timeIntervals, unique(timeIntervals));
            [~, maxIdx] = max(counts);
            expectedInterval = values(maxIdx);
            tolerance = 0.01 * expectedInterval;
            anomalyIndices = find(abs(timeIntervals - expectedInterval) > tolerance);
            anomalyValues = timeIntervals(anomalyIndices);
            stats.min = min(timeIntervals);
            stats.max = max(timeIntervals);
            stats.mean = mean(timeIntervals);
            stats.std = std(timeIntervals);
            stats.median = median(timeIntervals);
            isValid = isempty(anomalyIndices);
            droppedFrames = struct('count', 0, 'locations', [], 'framesPerDrop', []);
            if ~isempty(anomalyIndices)
                droppedFramesLocations = anomalyIndices;
                droppedFramesCount = zeros(size(droppedFramesLocations));
                for i = 1:length(anomalyIndices)
                    idx = anomalyIndices(i);
                    actualInterval = timeIntervals(idx);
                    lostFrames = round(actualInterval / expectedInterval) - 1;
                    if lostFrames < 1
                        if actualInterval > (1.5 * expectedInterval)
                            lostFrames = 1;
                        else
                            lostFrames = 0;
                        end
                    end
                    droppedFramesCount(i) = lostFrames;
                end
                validDrops = droppedFramesCount > 0;
                droppedFramesLocations = droppedFramesLocations(validDrops);
                droppedFramesCount = droppedFramesCount(validDrops);
                totalDroppedFrames = sum(droppedFramesCount);
                droppedFrames.count = totalDroppedFrames;
                droppedFrames.locations = droppedFramesLocations;
                droppedFrames.framesPerDrop = droppedFramesCount;
            end
            details.expectedInterval = expectedInterval;
            details.actualIntervals = timeIntervals;
            details.anomalies.indices = anomalyIndices;
            details.anomalies.values = anomalyValues;
            details.statistics = stats;
            details.gnssTimeData = gnssTimeData;
            details.droppedFrames = droppedFrames;
        end
    end
end