classdef ComNavResult
    % 组合导航结果类
    % 用于管理一次组合导航的结果，包括IMU、GNSS、位置、速度、姿态等信息

    properties
        utcTime     % UTC时间，datetime数组或数值型
        gyro        % 三轴陀螺输出，Nx3矩阵
        accel       % 三轴加计输出，Nx3矩阵
        satNum      % 卫星数，int数组
        gnssWeek    % GNSS周，int数组
        gnssTow     % GNSS周内秒，double数组
        rtkStatus   % RTK状态位，int数组或logical数组
        position    % 位置，Nx3矩阵（经度、纬度、高度）
        velocity    % 速度，Nx3矩阵（东、北、天）
        attitude    % 姿态，Nx3矩阵（横滚、俯仰、航向，单位：度）
        otherStatus % 其他状态信息，结构体或cell
        GNSSdata    % 结构体，包含速度、姿态、位置等GNSS相关子字段
        outputRate  % 数据输出频率 (Hz)
        leverarm    % 杆臂属性，结构体，包含安装误差角和三维杆臂
        PPSdelay    % PPS延迟，单位：秒
        gnss_PPSdelay % 如果该组合导航类包含来自于文件的卫导数据，则该属性表示卫导数据与INS数据之间的PPS延迟
    end

    properties (Constant)
        RE = 6378137.0; % 地球半长轴半径，单位：m
        F = 3.3528131778969144060323814696721e-3; % 扁率(1/298.257)
    end

    methods
        function obj = ComNavResult()
            % 构造函数，初始化为空
            obj.PPSdelay = 0; % 默认PPS延迟为0
        end
        function obj = setUtcTime(obj, utcTime)
            obj.utcTime = utcTime;
        end
        function obj = setGyro(obj, gyro)
            obj.gyro = gyro;
        end
        function obj = setAccel(obj, accel)
            obj.accel = accel;
        end
        function obj = setSatNum(obj, satNum)
            obj.satNum = satNum;
        end
        function obj = setGnssWeek(obj, gnssWeek)
            obj.gnssWeek = gnssWeek;
        end
        function obj = setGnssTow(obj, gnssTow)
            obj.gnssTow = gnssTow;
        end
        function obj = setRtkStatus(obj, rtkStatus)
            obj.rtkStatus = rtkStatus;
        end
        function obj = setPosition(obj, position)
            obj.position = position;
        end
        function obj = setVelocity(obj, velocity)
            obj.velocity = velocity;
        end
        function obj = setAttitude(obj, attitude)
            obj.attitude = attitude;
        end
        function obj = setOtherStatus(obj, otherStatus)
            obj.otherStatus = otherStatus;
        end
        function obj = setGNSSdata(obj, GNSSdata)
            obj.GNSSdata = GNSSdata;
        end
        function obj = setOutputRate(obj, outputRate)
            obj.outputRate = outputRate;
        end
        function obj = setLeverarm(obj, installAngles, leverarmVec)
            % setLeverarm - 设置杆臂属性
            % installAngles: [roll pitch yaw] (度)
            % leverarmVec: [dx dy dz] (米)
            obj.leverarm = struct('installAngles', installAngles, 'leverarmVec', leverarmVec);
        end
        function obj = setPPSdelay(obj, PPSdelay)
            % 设置PPS延迟，单位：秒
            obj.PPSdelay = PPSdelay;
        end
        function obj = setGnssPPSdelay(obj, gnss_PPSdelay)
            % 设置gnss_PPSdelay，单位：秒
            obj.gnss_PPSdelay = gnss_PPSdelay;
        end
        % 可根据需要添加可视化、导出等方法

        function comparePosition(obj, otherObj)
            % comparePosition - 横向对比两个ComNavResult对象的位置信息
            % 输入: otherObj - 另一个ComNavResult对象
            % 1. 绘制两个对象的经度、纬度、高度在同一子图内
            % 2. 通过gnssTow对齐，计算偏差并在yyaxis right上绘制

            if isempty(obj.position) || isempty(otherObj.position) || isempty(obj.gnssTow) || isempty(otherObj.gnssTow)
                error('两个对象的位置信息或gnssTow为空，无法对比');
            end

            % 取出数据
            pos1 = obj.position;
            pos2 = otherObj.position;
            tow1 = obj.gnssTow(:);
            tow2 = otherObj.gnssTow(:);

            % 杆臂补偿（新逻辑：先安装误差角整体旋转，再每帧用主对象姿态角b2n，补偿到经纬度/高程）
            if isprop(otherObj, 'leverarm') && ~isempty(otherObj.leverarm) && isfield(otherObj.leverarm, 'installAngles') && isfield(otherObj.leverarm, 'leverarmVec')
                att = [];
                if isprop(obj, 'attitude') && ~isempty(obj.attitude)
                    att = obj.attitude;
                end
                if isprop(otherObj, 'leverarm') && ~isempty(otherObj.leverarm) && isfield(otherObj.leverarm, 'installAngles') && isfield(otherObj.leverarm, 'leverarmVec')
                    pos2 = obj.applyLeverarmCompensation(pos2, att, otherObj.leverarm);
                end
            end

            tow2 = tow2 - otherObj.PPSdelay; 

            % 只对有交集的gnssTow进行对比
            [commonTow, idx1, idx2] = intersect(tow1, tow2);
            if isempty(commonTow)
                warning('两个对象的gnssTow没有交集，无法对齐对比');
            end
            % 画图
            figure;
            titles = {'经度', '纬度', '高度'};
            ylabels = {'经度(°)', '纬度(°)', '高度(m)'};
            for i = 1:3
                subplot(3,1,i);
                plot(tow1, pos1(:,i), 'b-', 'DisplayName', '对象1'); hold on;
                plot(tow2, pos2(:,i), 'r--', 'DisplayName', '对象2');
                if ~isempty(commonTow)
                    yyaxis right;
                    diffVal = pos1(idx1,i) - pos2(idx2,i);
                    plot(commonTow, diffVal, 'k:', 'LineWidth', 1.2, 'DisplayName', '差值(对象1-对象2)');
                    hold on
                    yline(0, '--', 'Color', [0.5 0.5 0.5], 'LineWidth', 1, 'DisplayName', '参考线');
                    ylabel('差值');
                end
                yyaxis left;
                ylabel(ylabels{i});
                title(['位置对比 - ' titles{i}]);
                legend;
                grid on;
            end
            xlabel('GNSS周内秒');

            % 新增：计算每帧的水平距离偏差并绘制（只对有交集的gnssTow，且用pos1/pos2）
            if ~isempty(commonTow)
                horizDist = zeros(length(idx1),1);
                for k = 1:length(idx1)
                    p1 = pos1(idx1(k),1:2);
                    p2 = pos2(idx2(k),1:2);
                    horizDist(k) = calculateDistanceBetweenDeg(p1, p2);
                end
                figure;
                plot(commonTow, horizDist, 'b-', 'LineWidth', 1.5);
                xlabel('GNSS周内秒');
                ylabel('水平距离偏差 (m)');
                title('每帧水平距离偏差曲线');
                grid on;
            end
        end

        function plotHorizontalTrack(obj)
            % plotHorizontalTrack - 将经纬度轨迹投影到本地坐标系并绘制，颜色随时间渐变
            if isempty(obj.position) || isempty(obj.gnssTow)
                error('位置信息或gnssTow为空，无法绘制轨迹');
            end
            lon = obj.position(:,1);
            lat = obj.position(:,2);
            t = obj.gnssTow(:);
            if length(lon) ~= length(t) || length(lat) ~= length(t)
                error('经纬度与时间长度不一致');
            end
            % 按照outputRate属性，1秒采样一次
            if isprop(obj, 'outputRate') && ~isempty(obj.outputRate) && ~isnan(obj.outputRate) && obj.outputRate > 0
                step = round(obj.outputRate);
            else
                step = 1; % fallback
            end
            idx = 1:step:length(t);
            if idx(end) ~= length(t)
                idx = [idx, length(t)];
            end
            % 不绘制最后一个点
            if length(idx) > 1
                idx = idx(1:end-1);
            end
            x = zeros(length(idx),1); % 东向
            y = zeros(length(idx),1); % 北向
            for k = 1:length(idx)
                x(k) = calculateDistanceBetweenDeg([lon(idx(1)), lat(idx(1))], [lon(idx(k)), lat(idx(1))]);
                if lon(idx(k)) < lon(idx(1))
                    x(k) = -x(k);
                end
                y(k) = calculateDistanceBetweenDeg([lon(idx(1)), lat(idx(1))], [lon(idx(1)), lat(idx(k))]);
                if lat(idx(k)) < lat(idx(1))
                    y(k) = -y(k);
                end
            end
            tNorm = (t(idx) - min(t)) / (max(t) - min(t) + eps);
            cmap = jet(256);
            colorIdx = round(1 + tNorm * 255);
            colorIdx(colorIdx < 1) = 1; colorIdx(colorIdx > 256) = 256;
            figure; hold on;
            for i = 1:length(x)-1
                c = cmap(colorIdx(i),:);
                plot(x(i:i+1), y(i:i+1), '-', 'Color', c, 'LineWidth', 2);
            end
            sctObj1 = scatter(x(1), y(1), 50, cmap(1,:), 'filled');
            % 不再绘制终点
            xlabel('东向 (m)'); ylabel('北向 (m)');
            title('本地坐标系下水平运动轨迹（颜色随时间渐变）');
            colorbar('Ticks',[0,1],'TickLabels',{'起点','终点'});
            colormap(jet);
            % axis equal;
            grid on;
            legend(sctObj1, {'起点'}, 'Location', 'best');
        end

        function plotCompareTrack(obj, otherObj)
            % plotCompareTrack - 将两个ComNavResult对象的经纬度轨迹绘制在同一图中
            if isempty(obj.position) || isempty(otherObj.position)
                error('两个对象的位置信息为空，无法绘制轨迹');
            end
            lon1 = obj.position(:,1);
            lat1 = obj.position(:,2);
            lon2 = otherObj.position(:,1);
            lat2 = otherObj.position(:,2);
            N1 = length(lon1);
            N2 = length(lon2);
            idx1 = 1:N1;
            idx2 = 1:N2;
            figure; hold on;
            plot(lon1(idx1), lat1(idx1), 'b-', 'LineWidth', 2, 'DisplayName', '对象1');
            plot(lon2(idx2), lat2(idx2), 'r--', 'LineWidth', 2, 'DisplayName', '对象2');
            scatter(lon1(idx1(1)), lat1(idx1(1)), 50, 'bo', 'filled', 'DisplayName', '对象1起点');
            scatter(lon2(idx2(1)), lat2(idx2(1)), 50, 'ro', 'filled', 'DisplayName', '对象2起点');
            scatter(lon1(idx1(end)), lat1(idx1(end)), 50, 'bs', 'filled', 'DisplayName', '对象1终点');
            scatter(lon2(idx2(end)), lat2(idx2(end)), 50, 'rs', 'filled', 'DisplayName', '对象2终点');
            xlabel('经度 (°)'); ylabel('纬度 (°)');
            title('两个对象的经纬度轨迹对比');
            legend('show', 'Location', 'best');
            grid on; axis equal;
        end

        function comparePositionGNSS(obj)
            % comparePositionGNSS - 对比自身GNSSdata与组合导航解算的位置信息
            % 1. 绘制经度、纬度、高度在同一子图内
            % 2. 对齐gnssTow，计算偏差并在yyaxis right上绘制

            if isempty(obj.position) || isempty(obj.gnssTow)
                error('对象的组合导航位置信息或gnssTow为空，无法对比');
            end
            if ~isfield(obj.GNSSdata, 'Position') || isempty(obj.GNSSdata.Position)
                error('对象的GNSSdata.Position为空，无法对比');
            end
            % 组合导航解算
            pos1 = obj.position;
            tow1 = obj.gnssTow(:);
            % GNSS原始
            pos2 = obj.GNSSdata.Position;
            if size(pos2,2) ~= 3
                error('GNSSdata.Position应为Nx3矩阵');
            end
            % 计算GNSS数据帧的周内秒（仅在GNSS数据发生变化时记录时刻）
            tow2 = [];
            lastPos = nan(1,3);
            for ii = 1:length(tow1)
                if any(isnan(lastPos)) || any(abs(pos2(ii,:) - lastPos) > 1e-10)
                    tow2 = [tow2; tow1(ii) - obj.gnss_PPSdelay];
                    lastPos = pos2(ii,:);
                end
            end
            % 只保留GNSS数据更新的帧
            idxGNSS = zeros(length(tow2),1);
            lastPos = nan(1,3);
            cnt = 1;
            for ii = 1:length(pos2)
                if any(isnan(lastPos)) || any(abs(pos2(ii,:) - lastPos) > 1e-10)
                    idxGNSS(cnt) = ii;
                    lastPos = pos2(ii,:);
                    cnt = cnt + 1;
                end
            end
            pos2 = pos2(idxGNSS,:);

            % 杆臂补偿（新逻辑：先安装误差角整体旋转，再每帧用姿态角b2n，补偿到经纬度/高程）
            if isprop(obj, 'leverarm') && ~isempty(obj.leverarm) && isfield(obj.leverarm, 'installAngles') && isfield(obj.leverarm, 'leverarmVec')
                att = [];
                if isprop(obj, 'attitude') && ~isempty(obj.attitude)
                    att = obj.attitude(idxGNSS,:); % 只取GNSS帧对应的姿态
                end
                pos2 = obj.applyLeverarmCompensation(pos2, att); % 默认用obj.leverarm
            end

            % 对齐gnssTow
            [commonTow, idx1, idx2] = intersect(tow1, tow2);
            if isempty(commonTow)
                warning('gnssTow没有交集，无法对齐对比');
            end
            % 画图
            figure;
            titles = {'经度', '纬度', '高度'};
            ylabels = {'经度(°)', '纬度(°)', '高度(m)'};
            for i = 1:3
                subplot(3,1,i);
                plot(tow1, pos1(:,i), 'b-', 'DisplayName', '组合导航'); hold on;
                plot(tow2, pos2(:,i), 'r--', 'DisplayName', 'GNSS');
                if ~isempty(commonTow)
                    yyaxis right;
                    diffVal = pos1(idx1,i) - pos2(idx2,i);
                    plot(commonTow, diffVal, 'k:', 'LineWidth', 1.2, 'DisplayName', '差值(组合-gnss)');
                    ylabel('差值');
                end
                yyaxis left;
                ylabel(ylabels{i});
                title(['位置对比 - ' titles{i}]);
                legend;
                grid on;
            end
            xlabel('GNSS周内秒'); 
            % 新增：计算每帧的水平距离偏差并绘制（只对有交集的gnssTow，且用pos1/pos2）
            if ~isempty(commonTow)
                horizDist = zeros(length(idx1),1);
                for k = 1:length(idx1)
                    p1 = pos1(idx1(k),1:2);
                    p2 = pos2(idx2(k),1:2);
                    horizDist(k) = calculateDistanceBetweenDeg(p1, p2);
                end
                figure;
                plot(commonTow, horizDist, 'b-', 'LineWidth', 1.5);
                xlabel('GNSS周内秒');
                ylabel('水平距离偏差 (m)');
                title('每帧水平距离偏差曲线');
                grid on;
            end
        end

        function plotCompareTrackGNSS(obj)
            % plotCompareTrackGNSS - 对比自身GNSSdata与组合导航解算的经纬度轨迹
            if isempty(obj.position)
                error('对象的组合导航位置信息为空，无法绘制轨迹');
            end
            if ~isfield(obj.GNSSdata, 'Position') || isempty(obj.GNSSdata.Position)
                error('对象的GNSSdata.Position为空，无法绘制轨迹');
            end
            lon1 = obj.position(:,1);
            lat1 = obj.position(:,2);
            lon2 = obj.GNSSdata.Position(:,1);
            lat2 = obj.GNSSdata.Position(:,2);
            N1 = length(lon1);
            N2 = length(lon2);
            % 按outputRate采样
            if isprop(obj, 'outputRate') && ~isempty(obj.outputRate) && ~isnan(obj.outputRate) && obj.outputRate > 0
                step1 = round(obj.outputRate);
            else
                step1 = 1;
            end
            idx1 = 1:step1:N1; if idx1(end) ~= N1, idx1 = [idx1, N1]; end
            if isprop(obj, 'outputRate') && ~isempty(obj.outputRate) && ~isnan(obj.outputRate) && obj.outputRate > 0
                step2 = round(obj.outputRate);
            else
                step2 = 1;
            end
            idx2 = 1:step2:N2; if idx2(end) ~= N2, idx2 = [idx2, N2]; end
            figure; hold on;
            plot(lon1(idx1), lat1(idx1), 'b-', 'LineWidth', 2, 'DisplayName', '组合导航');
            plot(lon2(idx2), lat2(idx2), 'r--', 'LineWidth', 2, 'DisplayName', 'GNSS');
            scatter(lon1(idx1(1)), lat1(idx1(1)), 50, 'bo', 'filled', 'DisplayName', '组合导航起点');
            scatter(lon2(idx2(1)), lat2(idx2(1)), 50, 'ro', 'filled', 'DisplayName', 'GNSS起点');
            scatter(lon1(idx1(end)), lat1(idx1(end)), 50, 'bs', 'filled', 'DisplayName', '组合导航终点');
            scatter(lon2(idx2(end)), lat2(idx2(end)), 50, 'rs', 'filled', 'DisplayName', 'GNSS终点');
            xlabel('经度 (°)'); ylabel('纬度 (°)');
            title('组合导航与GNSS经纬度轨迹对比');
            legend('show', 'Location', 'best');
            grid on; axis equal;
        end
        
        function plotCompareHorizontalTrackGNSS(obj)
            % plotCompareHorizontalTrackGNSS - 对比组合导航轨迹和GNSSdata经过杆臂补偿后的轨迹
            if isempty(obj.position) || isempty(obj.gnssTow)
                error('组合导航位置信息或gnssTow为空，无法绘制轨迹');
            end
            if ~isfield(obj.GNSSdata, 'Position') || isempty(obj.GNSSdata.Position)
                error('GNSSdata.Position为空，无法绘制轨迹');
            end
            % 组合导航轨迹
            lon_nav = obj.position(:,1);
            lat_nav = obj.position(:,2);
            % GNSS轨迹（补偿杆臂）
            pos_gnss = obj.GNSSdata.Position;
            att = [];
            if isprop(obj, 'attitude') && ~isempty(obj.attitude)
                att = obj.attitude;
            end
            pos_gnss = obj.applyLeverarmCompensation(pos_gnss, att); % 默认用obj.leverarm
            lon_gnss = pos_gnss(:,1);
            lat_gnss = pos_gnss(:,2);
            % 本地坐标投影（以组合导航首点为原点）
            lon0 = lon_nav(1); lat0 = lat_nav(1);
            N1 = length(lon_nav); N2 = length(lon_gnss);
            x_nav = zeros(N1,1); y_nav = zeros(N1,1);
            x_gnss = zeros(N2,1); y_gnss = zeros(N2,1);
            for k = 1:N1
                x_nav(k) = calculateDistanceBetweenDeg([lon0, lat0], [lon_nav(k), lat0]);
                if lon_nav(k) < lon0, x_nav(k) = -x_nav(k); end
                y_nav(k) = calculateDistanceBetweenDeg([lon0, lat0], [lon0, lat_nav(k)]);
                if lat_nav(k) < lat0, y_nav(k) = -y_nav(k); end
            end
            for k = 1:N2
                x_gnss(k) = calculateDistanceBetweenDeg([lon0, lat0], [lon_gnss(k), lat0]);
                if lon_gnss(k) < lon0, x_gnss(k) = -x_gnss(k); end
                y_gnss(k) = calculateDistanceBetweenDeg([lon0, lat0], [lon0, lat_gnss(k)]);
                if lat_gnss(k) < lat0, y_gnss(k) = -y_gnss(k); end
            end
            figure; hold on;
            plot(x_nav, y_nav, 'b-', 'LineWidth', 2, 'DisplayName', '组合导航轨迹');
            plot(x_gnss, y_gnss, 'r--', 'LineWidth', 2, 'DisplayName', 'GNSS(补偿后)轨迹');
            scatter(x_nav(1), y_nav(1), 50, 'bo', 'filled', 'DisplayName', '组合导航起点');
            scatter(x_gnss(1), y_gnss(1), 50, 'ro', 'filled', 'DisplayName', 'GNSS起点');
            xlabel('东向 (m)'); ylabel('北向 (m)');
            title('组合导航与GNSS(补偿后)本地坐标系下水平轨迹对比');
            legend('show', 'Location', 'best');
            axis equal; grid on;
        end
        
        function comparePositionByFirstFrame(obj, otherObj)
            % comparePositionByFirstFrame - 以两组数据的第一帧对齐直接对比位置信息
            % 输入: otherObj - 另一个ComNavResult对象
            % 1. 绘制两个对象的经度、纬度、高度在同一子图内
            % 2. 直接计算差值并在yyaxis right上绘制

            if isempty(obj.position) || isempty(otherObj.position)
                error('两个对象的位置信息为空，无法对比');
            end
            pos1 = obj.position;
            pos2 = otherObj.position;
            n = min(size(pos1,1), size(pos2,1));
            pos1 = pos1(1:n, :);
            pos2 = pos2(1:n, :);
            % 画图
            figure;
            titles = {'经度', '纬度', '高度'};
            ylabels = {'经度(°)', '纬度(°)', '高度(m)'};
            for i = 1:3
                subplot(3,1,i);
                plot(1:n, pos1(:,i), 'b-', 'DisplayName', '对象1'); hold on;
                plot(1:n, pos2(:,i), 'r--', 'DisplayName', '对象2');
                yyaxis right;
                diffVal = pos1(:,i) - pos2(:,i);
                plot(1:n, diffVal, 'k:', 'LineWidth', 1.2, 'DisplayName', '差值(对象1-对象2)');
                ylabel('差值');
                yyaxis left;
                ylabel(ylabels{i});
                title(['位置对比(首帧对齐) - ' titles{i}]);
                legend;
                grid on;
            end
            xlabel('帧号');
        end
        
        function subObj = subResultByGnssTowRange(obj, gnssTowStart, gnssTowEnd)
            % subResultByGnssTowRange - 根据GNSS周内秒范围生成子组合导航类
            % 输入: gnssTowStart, gnssTowEnd - GNSS周内秒的起止范围
            % 输出: subObj - 新的ComNavResult对象，仅包含指定范围内的数据
            idx = find(obj.gnssTow >= gnssTowStart & obj.gnssTow <= gnssTowEnd);
            if isempty(idx)
                warning('指定范围内无数据，返回空对象');
                subObj = ComNavResult();
                return;
            end
            subObj = ComNavResult();
            props = properties(obj);
            for i = 1:length(props)
                val = obj.(props{i});
                if isempty(val)
                    continue; % 跳过空属性
                end
                if strcmp(props{i},'outputRate')
                    continue;
                end
                if isnumeric(val) || islogical(val)
                    if isvector(val)
                        subObj.(props{i}) = val(idx);
                    elseif ismatrix(val) && size(val,1) == length(obj.gnssTow)
                        subObj.(props{i}) = val(idx,:);
                    else
                        subObj.(props{i}) = val;
                    end
                elseif isstruct(val) && isfield(val, 'Position')
                    subObj.GNSSdata = val;
                    if isfield(val, 'Position') && size(val.Position,1) == length(obj.gnssTow)
                        subObj.GNSSdata.Position = val.Position(idx,:);
                    end
                    if isfield(val, 'Velocity') && size(val.Velocity,1) == length(obj.gnssTow)
                        subObj.GNSSdata.Velocity = val.Velocity(idx,:);
                    end
                    if isfield(val, 'Attitude') && size(val.Attitude,1) == length(obj.gnssTow)
                        subObj.GNSSdata.Attitude = val.Attitude(idx,:);
                    end
                else
                    subObj.(props{i}) = val;
                end
            end
        end
        
        function [invRm, invRn] = calculateInvRmInvRn(obj, latiRad, height)
            % calculateInvRmInvRn - 计算子午圈半径倒数和卯酉圈半径倒数
            % 输入: latiRad - 纬度(弧度)
            %      height - 高程(米)
            % 输出: invRm - 子午圈半径倒数
            %        invRn - 卯酉圈半径倒数
            SinLati = sin(latiRad);
            SinLatiSqure = SinLati .* SinLati;
            Rm = obj.RE - (2 * obj.RE * obj.F) + (3 * obj.RE * obj.F * SinLatiSqure) + height;
            Rn = obj.RE + obj.F * obj.RE * SinLatiSqure + height;
            invRm = 1 ./ Rm;
            invRn = 1 ./ Rn;
        end
        
        function posComp = applyLeverarmCompensation(obj, pos, att, leverarmStruct)
            % applyLeverarmCompensation - 对输入的pos(Nx3)和att(Nx3)进行杆臂补偿，返回补偿后的posComp
            % pos: [lon, lat, h] (度,度,米)
            % att: [roll, pitch, heading] (度,度,度)
            % leverarmStruct: 杆臂结构体（可选，优先使用）
            % 返回: posComp (Nx3, [lon, lat, h], 单位与输入一致)
            if nargin < 4 || isempty(leverarmStruct)
                if ~isprop(obj, 'leverarm') || isempty(obj.leverarm) || ~isfield(obj.leverarm, 'installAngles') || ~isfield(obj.leverarm, 'leverarmVec')
                    posComp = pos;
                    return;
                end
                leverarmStruct = obj.leverarm;
            end
            if isempty(leverarmStruct) || ~isfield(leverarmStruct, 'installAngles') || ~isfield(leverarmStruct, 'leverarmVec')
                posComp = pos;
                return;
            end
            leverarm_b = leverarmStruct.leverarmVec(:); % 列向量
            rpy_install = leverarmStruct.installAngles * pi/180;
            Rx = [1 0 0; 0 cos(rpy_install(1)) -sin(rpy_install(1)); 0 sin(rpy_install(1)) cos(rpy_install(1))];
            Ry = [cos(rpy_install(2)) 0 sin(rpy_install(2)); 0 1 0; -sin(rpy_install(2)) 0 cos(rpy_install(2))];
            Rz = [cos(rpy_install(3)) -sin(rpy_install(3)) 0; sin(rpy_install(3)) cos(rpy_install(3)) 0; 0 0 1];
            R_install = Rz * Ry * Rx;
            leverarm_b_corr = R_install * leverarm_b;
            N = size(pos,1);
            posComp = pos;
            if nargin < 3 || isempty(att)
                posComp = pos + repmat(leverarm_b_corr', N, 1);
                return;
            end
            leverarm_n = zeros(N,3);
            for k = 1:N
                rpy = att(k,:) * pi/180;
                Rx = [1 0 0; 0 cos(rpy(1)) -sin(rpy(1)); 0 sin(rpy(1)) cos(rpy(1))];
                Ry = [cos(rpy(2)) 0 sin(rpy(2)); 0 1 0; -sin(rpy(2)) 0 cos(rpy(2))];
                Rz = [cos(rpy(3)) -sin(rpy(3)) 0; sin(rpy(3)) cos(rpy(3)) 0; 0 0 1];
                Rb2n = Rz * Ry * Rx;
                leverarm_n(k,:) = (Rb2n * leverarm_b_corr)';
            end
            lat_rad = deg2rad(pos(:,2));
            h = pos(:,3);
            [invRm, invRn] = obj.calculateInvRmInvRn(lat_rad, h);
            posComp(:,2) = lat_rad - leverarm_n(:,2) .* invRm; % 纬度(弧度)
            posComp(:,1) = deg2rad(pos(:,1)) - leverarm_n(:,1) .* invRn; % 经度(弧度)
            posComp(:,3) = pos(:,3) + leverarm_n(:,3);
            posComp(:,2) = rad2deg(posComp(:,2));
            posComp(:,1) = rad2deg(posComp(:,1));
        end
        
        function gnssObj = extractGnssComNavResult(obj)
            % extractGnssComNavResult - 将GNSSdata分离为独立的ComNavResult对象，仅保留GNSS数据更新帧
            if ~isfield(obj.GNSSdata, 'Position') || isempty(obj.GNSSdata.Position)
                error('对象的GNSSdata.Position为空，无法分离');
            end
            pos = obj.GNSSdata.Position;
            tow = obj.gnssTow(:);
            % 计算GNSS数据帧的周内秒（仅在GNSS数据发生变化时记录时刻）
            tow2 = [];
            lastPos = nan(1,3);
            for ii = 1:length(tow)
                if any(isnan(lastPos)) || any(abs(pos(ii,:) - lastPos) > 1e-10)
                    tow2 = [tow2; tow(ii)-obj.PPSdelay];
                    lastPos = pos(ii,:);
                end
            end
            % 只保留GNSS数据更新的帧
            idxGNSS = zeros(length(tow2),1);
            lastPos = nan(1,3);
            cnt = 1;
            for ii = 1:length(pos)
                if any(isnan(lastPos)) || any(abs(pos(ii,:) - lastPos) > 1e-10)
                    idxGNSS(cnt) = ii;
                    lastPos = pos(ii,:);
                    cnt = cnt + 1;
                end
            end
            % 构造新ComNavResult对象
            gnssObj = ComNavResult();
            gnssObj.gnssTow = tow2;
            gnssObj.position = pos(idxGNSS,:);
            if isfield(obj.GNSSdata, 'Velocity') && ~isempty(obj.GNSSdata.Velocity)
                gnssObj.velocity = obj.GNSSdata.Velocity(idxGNSS,:);
            end
            if isfield(obj.GNSSdata, 'Attitude') && ~isempty(obj.GNSSdata.Attitude)
                gnssObj.attitude = obj.GNSSdata.Attitude(idxGNSS,:);
            end
            if isfield(obj.GNSSdata, 'satNum') && ~isempty(obj.GNSSdata.satNum)
                gnssObj.satNum = obj.GNSSdata.satNum(idxGNSS);
            end
            if isfield(obj.GNSSdata, 'rtkStatus') && ~isempty(obj.GNSSdata.rtkStatus)
                gnssObj.rtkStatus = obj.GNSSdata.rtkStatus(idxGNSS);
            end
            % 其他属性可根据需要补充
        end
    end
end
