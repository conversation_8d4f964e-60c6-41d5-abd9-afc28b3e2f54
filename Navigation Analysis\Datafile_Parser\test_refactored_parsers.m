function test_refactored_parsers()
    % test_refactored_parsers - 测试重构后的parser类
    % 验证重构后的类功能是否正常
    
    fprintf('=== 测试重构后的Parser类 ===\n\n');
    
    % 测试1: 验证类的继承关系
    fprintf('1. 测试类的继承关系...\n');
    test_inheritance();
    
    % 测试2: 验证抽象方法的实现
    fprintf('\n2. 测试抽象方法的实现...\n');
    test_abstract_methods();
    
    % 测试3: 验证公共方法的可用性
    fprintf('\n3. 测试公共方法的可用性...\n');
    test_public_methods();
    
    fprintf('\n=== 所有测试完成 ===\n');
end

function test_inheritance()
    % 测试类的继承关系
    try
        % 测试BB00Parser
        fprintf('  - BB00Parser继承关系: ');
        mc_bb00 = ?BB00Parser;
        if any(strcmp({mc_bb00.SuperclassList.Name}, 'DatafileParser'))
            fprintf('✓ 正确继承自DatafileParser\n');
        else
            fprintf('✗ 继承关系错误\n');
        end
        
        % 测试BDDBParser
        fprintf('  - BDDBParser继承关系: ');
        mc_bddb = ?BDDBParser;
        if any(strcmp({mc_bddb.SuperclassList.Name}, 'DatafileParser'))
            fprintf('✓ 正确继承自DatafileParser\n');
        else
            fprintf('✗ 继承关系错误\n');
        end
        
        % 测试BDDB1BParser
        fprintf('  - BDDB1BParser继承关系: ');
        mc_bddb1b = ?BDDB1BParser;
        if any(strcmp({mc_bddb1b.SuperclassList.Name}, 'DatafileParser'))
            fprintf('✓ 正确继承自DatafileParser\n');
        else
            fprintf('✗ 继承关系错误\n');
        end
        
    catch ME
        fprintf('✗ 测试继承关系时出错: %s\n', ME.message);
    end
end

function test_abstract_methods()
    % 测试抽象方法的实现
    try
        % 检查BB00Parser是否实现了parseCSV方法
        fprintf('  - BB00Parser.parseCSV方法: ');
        mc_bb00 = ?BB00Parser;
        methods_bb00 = {mc_bb00.MethodList.Name};
        if any(strcmp(methods_bb00, 'parseCSV'))
            fprintf('✓ 已实现\n');
        else
            fprintf('✗ 未实现\n');
        end
        
        % 检查BDDBParser是否实现了parseCSV方法
        fprintf('  - BDDBParser.parseCSV方法: ');
        mc_bddb = ?BDDBParser;
        methods_bddb = {mc_bddb.MethodList.Name};
        if any(strcmp(methods_bddb, 'parseCSV'))
            fprintf('✓ 已实现\n');
        else
            fprintf('✗ 未实现\n');
        end
        
        % 检查BDDB1BParser是否实现了parseCSV方法
        fprintf('  - BDDB1BParser.parseCSV方法: ');
        mc_bddb1b = ?BDDB1BParser;
        methods_bddb1b = {mc_bddb1b.MethodList.Name};
        if any(strcmp(methods_bddb1b, 'parseCSV'))
            fprintf('✓ 已实现\n');
        else
            fprintf('✗ 未实现\n');
        end
        
    catch ME
        fprintf('✗ 测试抽象方法时出错: %s\n', ME.message);
    end
end

function test_public_methods()
    % 测试公共方法的可用性
    try
        % 测试BB00Parser的公共方法
        fprintf('  - BB00Parser公共方法: ');
        mc_bb00 = ?BB00Parser;
        public_methods = {mc_bb00.MethodList([mc_bb00.MethodList.Access] == "public").Name};
        expected_methods = {'getData', 'saveToMat', 'validateColumns', 'toComNavResult'};
        
        missing_methods = setdiff(expected_methods, public_methods);
        if isempty(missing_methods)
            fprintf('✓ 所有公共方法可用\n');
        else
            fprintf('✗ 缺少方法: %s\n', strjoin(missing_methods, ', '));
        end
        
        % 测试BDDBParser的公共方法
        fprintf('  - BDDBParser公共方法: ');
        mc_bddb = ?BDDBParser;
        public_methods = {mc_bddb.MethodList([mc_bddb.MethodList.Access] == "public").Name};
        
        missing_methods = setdiff(expected_methods, public_methods);
        if isempty(missing_methods)
            fprintf('✓ 所有公共方法可用\n');
        else
            fprintf('✗ 缺少方法: %s\n', strjoin(missing_methods, ', '));
        end
        
        % 测试BDDB1BParser的公共方法
        fprintf('  - BDDB1BParser公共方法: ');
        mc_bddb1b = ?BDDB1BParser;
        public_methods = {mc_bddb1b.MethodList([mc_bddb1b.MethodList.Access] == "public").Name};
        
        missing_methods = setdiff(expected_methods, public_methods);
        if isempty(missing_methods)
            fprintf('✓ 所有公共方法可用\n');
        else
            fprintf('✗ 缺少方法: %s\n', strjoin(missing_methods, ', '));
        end
        
    catch ME
        fprintf('✗ 测试公共方法时出错: %s\n', ME.message);
    end
end
