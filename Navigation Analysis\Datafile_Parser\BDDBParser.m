classdef BDDBParser < DatafileParser
    % BDDBParser - 用于解析BDDB协议CSV文件并管理数据的类
    % 继承自DatafileParser抽象父类

    properties (Access = public)
        columnNames = {  % 定义预期的列顺序
            '横滚', '俯仰', '偏航', '陀螺x', '陀螺y', '陀螺z', ...
            '加计x', '加计y', '加计z', '纬度', '经度', '高度', ...
            '东向速度', '北向速度', '天向速度', '位置状态', ...
            '速度状态', '姿态状态', '航向状态', 'GNSS秒', ...
            'GNSS周', '查询类型', '温度', '卫星数', '轮速', ...
            'RTK状态', '纬度标准差', '经度标准差', '高度标准差', ...
            '北向速度标准差', '东向速度标准差', '天向速度标准差', ...
            '横滚标准差', '俯仰标准差', '偏航标准差'};
    end

    methods
        function obj = BDDBParser(filename)
            % BDDBParser - 构造函数
            % 输入:
            %   filename - 要解析的CSV文件名
            % 调用父类构造函数
            obj@DatafileParser(filename);
        end
    end

    methods (Access = protected)
        
        function parseCSV(obj)
            % parseCSV - 解析CSV文件并将数据存储到分层结构体中
            % 实现父类的抽象方法
            try
                % 使用父类的通用读取方法
                obj.readCSVFile();

                % 使用父类方法计算数据输出频率
                gnss_ms = obj.rawData{:,20}; % GNSS秒
                obj.calculateOutputRate(gnss_ms, 's');
                
                % 创建分层结构体
                obj.dataStruct = struct();
                
                % 1. 姿态数据
                obj.dataStruct.Attitude = struct(...
                    'Roll',     obj.rawData{:,1}, ...
                    'Pitch',    obj.rawData{:,2}, ...
                    'Yaw',      obj.rawData{:,3} ...
                    );
                
                % 2. IMU数据(陀螺+加速度计)
                obj.dataStruct.IMU = struct(...
                    'Gyro', struct(...
                        'X', obj.rawData{:,4}, ...
                        'Y', obj.rawData{:,5}, ...
                        'Z', obj.rawData{:,6}), ...
                    'Accel', struct(...
                        'X', obj.rawData{:,7}, ...
                        'Y', obj.rawData{:,8}, ...
                        'Z', obj.rawData{:,9}) ...
                    );
                
                % 3. GNSS位置数据
                obj.dataStruct.Position = struct(...
                    'Latitude',     obj.rawData{:,11}, ...
                    'Longitude',    obj.rawData{:,10}, ...
                    'Altitude',     obj.rawData{:,12}, ...
                    'Time', struct(...
                        'GNSSMilliseconds', obj.rawData{:,20}, ...
                        'GNSSWeek',        obj.rawData{:,21}) ...
                    );
                
                % 4. 速度数据
                obj.dataStruct.Velocity = struct(...
                    'East',     obj.rawData{:,13}, ...
                    'North',    obj.rawData{:,14}, ...
                    'Up',       obj.rawData{:,15} ...
                    );
                
                % 5. 状态信息
                obj.dataStruct.Status = struct(...
                    'Position', obj.rawData{:,16}, ...
                    'Velocity', obj.rawData{:,17}, ...
                    'Attitude', obj.rawData{:,18}, ...
                    'Heading',  obj.rawData{:,19}, ...
                    'RTK',      obj.rawData{:,26} ...
                    );
                
                % 6. 其他信息
                obj.dataStruct.Other = struct(...
                    'QueryType',    obj.rawData{:,22}, ...
                    'Temperature',  obj.rawData{:,23}, ...
                    'SatelliteCount', obj.rawData{:,24}, ...
                    'WheelSpeed',  obj.rawData{:,25} ...
                    );
                
                % 7. 标准差信息(按类别分组)
                obj.dataStruct.StdDev = struct(...
                    'Position', struct(...
                        'Latitude', obj.rawData{:,27}, ...
                        'Longitude', obj.rawData{:,28}, ...
                        'Altitude', obj.rawData{:,29}), ...
                    'Velocity', struct(...
                        'North', obj.rawData{:,30}, ...
                        'East',  obj.rawData{:,31}, ...
                        'Up',    obj.rawData{:,32}), ...
                    'Attitude', struct(...
                        'Roll',  obj.rawData{:,33}, ...
                        'Pitch', obj.rawData{:,34}, ...
                        'Yaw',   obj.rawData{:,35}) ...
                    );
                
                disp('CSV文件解析完成，数据已存储在分层的dataStruct属性中。');

            catch ME
                error('解析CSV文件时出错: %s\n请检查文件格式和列顺序是否与BDDB协议一致。', ME.message);
            end
        end
    end
end