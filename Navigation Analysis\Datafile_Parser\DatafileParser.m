classdef (Abstract) DatafileParser < handle
    % DatafileParser - 数据文件解析器抽象父类
    % 为不同协议的数据文件解析器提供统一的接口和共同功能
    
    properties (Access = protected)
        filename    % CSV文件名
        rawData     % 原始数据表
    end
    
    properties (Access = public)
        dataStruct  % 包含所有数据的结构体(包含子结构体)
        outputRate  % 数据输出频率 (Hz)
    end
    
    properties (Abstract, Access = public)
        columnNames % 列名定义，由子类具体实现
    end
    
    methods
        function obj = DatafileParser(filename)
            % DatafileParser - 构造函数
            % 输入:
            %   filename - 要解析的CSV文件名
            if nargin > 0
                obj.filename = filename;
                obj.parseCSV();
            end
        end
        
        function data = getData(obj)
            % getData - 获取解析后的数据
            % 输出:
            %   data - 包含所有数据的结构体
            if isempty(obj.dataStruct)
                error('数据未解析，请先调用parseCSV方法。');
            end
            data = obj.dataStruct;
        end
        
        function saveToMat(obj, outputFilename)
            % saveToMat - 将数据保存为MAT文件
            % 输入:
            %   outputFilename - 输出MAT文件名(可选)
            if isempty(obj.dataStruct)
                error('没有可保存的数据，请先解析文件。');
            end
            
            if nargin < 2
                [~, name, ~] = fileparts(obj.filename);
                outputFilename = [name '_parsed.mat'];
            end
            
            data = obj.dataStruct;
            save(outputFilename, 'data', '-v7.3');
            disp(['数据已保存到 ' outputFilename]);
        end
        
        function validateColumns(obj)
            % validateColumns - 验证CSV列顺序是否符合预期
            if ~isempty(obj.rawData)
                actualVars = obj.rawData.Properties.VariableNames;
                disp('=== 列顺序验证 ===');
                for i = 1:length(obj.columnNames)
                    if i <= length(actualVars)
                        fprintf('列 %2d: 预期[%s]\t实际[%s]\n', ...
                            i, obj.columnNames{i}, actualVars{i});
                    else
                        fprintf('列 %2d: 预期[%s]\t实际[缺失]\n', ...
                            i, obj.columnNames{i});
                    end
                end
            else
                warning('没有可验证的数据，请先解析文件。');
            end
        end
        
        function navObj = toComNavResult(obj)
            % toComNavResult - 基于解析的数据生成一个ComNavResult对象
            % 输出:
            %   navObj - ComNavResult类实例
            if isempty(obj.dataStruct)
                error('数据未解析，请先调用parseCSV方法。');
            end
            
            navObj = ComNavResult();
            
            % 通用的数据转换逻辑
            obj.setComNavIMUData(navObj);
            obj.setComNavPositionData(navObj);
            obj.setComNavVelocityData(navObj);
            obj.setComNavAttitudeData(navObj);
            obj.setComNavTimeData(navObj);
            obj.setComNavSatelliteData(navObj);
            obj.setComNavOtherData(navObj);
        end
    end
    
    methods (Abstract, Access = protected)
        % 抽象方法，由子类实现具体的解析逻辑
        parseCSV(obj)
        % parseCSV - 解析CSV文件并将数据存储到分层结构体中
        % 每个子类根据其协议特点实现具体的解析逻辑
    end
    
    methods (Access = protected)
        function readCSVFile(obj)
            % readCSVFile - 读取CSV文件的通用方法
            try
                opts = detectImportOptions(obj.filename);
                obj.rawData = readtable(obj.filename, opts);
                
                % 验证列数是否匹配
                if width(obj.rawData) ~= length(obj.columnNames)
                    error('列数不匹配! 预期%d列, 实际%d列', ...
                        length(obj.columnNames), width(obj.rawData));
                end
            catch ME
                error('读取CSV文件时出错: %s', ME.message);
            end
        end
        
        function calculateOutputRate(obj, timeColumn, timeUnit)
            % calculateOutputRate - 计算数据输出频率的通用方法
            % 输入:
            %   timeColumn - 时间列数据
            %   timeUnit - 时间单位 ('ms' 或 's')
            if nargin < 3
                timeUnit = 's'; % 默认为秒
            end
            
            if length(timeColumn) > 1
                diff_time = diff(timeColumn);
                
                % 处理时间回绕的情况（如周内秒）
                if strcmp(timeUnit, 's') && any(diff_time < -600000)
                    % 检测周内秒回绕
                    week_rollover_idx = diff_time < -600000;
                    diff_time(week_rollover_idx) = diff_time(week_rollover_idx) + 604800;
                end
                
                % 排除异常间隔
                med_dt = median(diff_time);
                valid_idx = diff_time > 0 & diff_time < 2 * med_dt;
                valid_diff = diff_time(valid_idx);
                
                if ~isempty(valid_diff)
                    mean_dt = mean(valid_diff);
                    if strcmp(timeUnit, 'ms')
                        mean_dt = mean_dt / 1000; % 转换为秒
                    end
                    obj.outputRate = 1 / mean_dt; % 转为Hz
                else
                    obj.outputRate = NaN;
                end
            else
                obj.outputRate = NaN;
            end
        end

        function setComNavIMUData(obj, navObj)
            % setComNavIMUData - 设置ComNavResult对象的IMU数据
            if isfield(obj.dataStruct, 'IMU')
                gyro = [obj.dataStruct.IMU.Gyro.X, obj.dataStruct.IMU.Gyro.Y, obj.dataStruct.IMU.Gyro.Z];
                accel = [obj.dataStruct.IMU.Accel.X, obj.dataStruct.IMU.Accel.Y, obj.dataStruct.IMU.Accel.Z];

                % 找到第一个有效数据帧
                first_valid = obj.findFirstValidFrame();
                if ~isempty(first_valid)
                    gyro = gyro(first_valid:end, :);
                    accel = accel(first_valid:end, :);
                end

                navObj = navObj.setGyro(gyro);
                navObj = navObj.setAccel(accel);
            end
        end

        function setComNavPositionData(obj, navObj)
            % setComNavPositionData - 设置ComNavResult对象的位置数据
            if isfield(obj.dataStruct, 'Position')
                pos = [obj.dataStruct.Position.Longitude, obj.dataStruct.Position.Latitude, obj.dataStruct.Position.Altitude];

                % 找到第一个有效数据帧
                first_valid = obj.findFirstValidFrame();
                if ~isempty(first_valid)
                    pos = pos(first_valid:end, :);
                end

                navObj = navObj.setPosition(pos);
            end
        end

        function setComNavVelocityData(obj, navObj)
            % setComNavVelocityData - 设置ComNavResult对象的速度数据
            if isfield(obj.dataStruct, 'Velocity')
                vel = [obj.dataStruct.Velocity.East, obj.dataStruct.Velocity.North, obj.dataStruct.Velocity.Up];

                % 找到第一个有效数据帧
                first_valid = obj.findFirstValidFrame();
                if ~isempty(first_valid)
                    vel = vel(first_valid:end, :);
                end

                navObj = navObj.setVelocity(vel);
            end
        end

        function setComNavAttitudeData(obj, navObj)
            % setComNavAttitudeData - 设置ComNavResult对象的姿态数据
            if isfield(obj.dataStruct, 'Attitude')
                att = [obj.dataStruct.Attitude.Roll, obj.dataStruct.Attitude.Pitch, obj.dataStruct.Attitude.Yaw];

                % 找到第一个有效数据帧
                first_valid = obj.findFirstValidFrame();
                if ~isempty(first_valid)
                    att = att(first_valid:end, :);
                end

                navObj = navObj.setAttitude(att);
            end
        end

        function setComNavTimeData(obj, navObj)
            % setComNavTimeData - 设置ComNavResult对象的时间数据
            first_valid = obj.findFirstValidFrame();

            % GNSS周和周内秒
            if isfield(obj.dataStruct, 'Time')
                if isfield(obj.dataStruct.Time, 'GNSSWeek')
                    gnssWeek = obj.dataStruct.Time.GNSSWeek;
                    if ~isempty(first_valid) && length(gnssWeek) >= first_valid
                        gnssWeek = gnssWeek(first_valid:end);
                    end
                    navObj = navObj.setGnssWeek(gnssWeek);
                end

                if isfield(obj.dataStruct.Time, 'GNSSSeconds')
                    gnssTow = obj.dataStruct.Time.GNSSSeconds;
                    if ~isempty(first_valid) && length(gnssTow) >= first_valid
                        gnssTow = gnssTow(first_valid:end);
                    end
                    navObj = navObj.setGnssTow(gnssTow);
                elseif isfield(obj.dataStruct.Time, 'GNSSMilliseconds')
                    gnssTow = obj.dataStruct.Time.GNSSMilliseconds ./ 1e3;
                    if ~isempty(first_valid) && length(gnssTow) >= first_valid
                        gnssTow = gnssTow(first_valid:end);
                    end
                    navObj = navObj.setGnssTow(gnssTow);
                end
            end

            % 设置输出频率
            if ~isempty(obj.outputRate) && ~isnan(obj.outputRate)
                navObj = navObj.setOutputRate(obj.outputRate);
            end
        end

        function setComNavSatelliteData(obj, navObj)
            % setComNavSatelliteData - 设置ComNavResult对象的卫星数据
            first_valid = obj.findFirstValidFrame();

            % 卫星数量
            satNum = [];
            if isfield(obj.dataStruct, 'SatInfo') && isfield(obj.dataStruct.SatInfo, 'SatelliteCount')
                satNum = obj.dataStruct.SatInfo.SatelliteCount;
            elseif isfield(obj.dataStruct, 'Other') && isfield(obj.dataStruct.Other, 'SatelliteCount')
                satNum = obj.dataStruct.Other.SatelliteCount;
            end

            if ~isempty(satNum)
                if ~isempty(first_valid) && length(satNum) >= first_valid
                    satNum = satNum(first_valid:end);
                end
                navObj = navObj.setSatNum(satNum);
            end

            % RTK状态
            rtkStatus = [];
            if isfield(obj.dataStruct, 'SatInfo') && isfield(obj.dataStruct.SatInfo, 'RTKStatus')
                rtkStatus = obj.dataStruct.SatInfo.RTKStatus;
            elseif isfield(obj.dataStruct, 'Status') && isfield(obj.dataStruct.Status, 'RTK')
                rtkStatus = obj.dataStruct.Status.RTK;
            end

            if ~isempty(rtkStatus)
                if ~isempty(first_valid) && length(rtkStatus) >= first_valid
                    rtkStatus = rtkStatus(first_valid:end);
                end
                navObj = navObj.setRtkStatus(rtkStatus);
            end
        end

        function setComNavOtherData(obj, navObj)
            % setComNavOtherData - 设置ComNavResult对象的其他数据
            first_valid = obj.findFirstValidFrame();

            % 复制整个数据结构作为其他状态
            otherStatus = obj.dataStruct;

            % 如果有有效帧，则截取数据
            if ~isempty(first_valid)
                otherStatus = obj.truncateDataStruct(otherStatus, first_valid);
            end

            navObj = navObj.setOtherStatus(otherStatus);
        end

        function first_valid = findFirstValidFrame(obj)
            % findFirstValidFrame - 查找第一个有效的数据帧
            first_valid = [];

            if isfield(obj.dataStruct, 'Position')
                pos = [obj.dataStruct.Position.Longitude, obj.dataStruct.Position.Latitude, obj.dataStruct.Position.Altitude];
                first_valid = find(~(pos(:,1)==0 & pos(:,2)==0 & pos(:,3)==0), 1, 'first');
            end

            if isempty(first_valid)
                first_valid = 1; % 如果没有找到，从第一帧开始
            end
        end

        function truncatedStruct = truncateDataStruct(obj, dataStruct, startIdx)
            % truncateDataStruct - 递归截取数据结构
            truncatedStruct = dataStruct;

            if isstruct(dataStruct)
                fields = fieldnames(dataStruct);
                for i = 1:length(fields)
                    field = fields{i};
                    if isstruct(dataStruct.(field))
                        truncatedStruct.(field) = obj.truncateDataStruct(dataStruct.(field), startIdx);
                    elseif isnumeric(dataStruct.(field)) && length(dataStruct.(field)) >= startIdx
                        truncatedStruct.(field) = dataStruct.(field)(startIdx:end);
                    end
                end
            end
        end
    end
end
